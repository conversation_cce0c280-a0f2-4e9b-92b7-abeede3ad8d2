import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    server: {
        host: 'localhost',
        port: 5173,
        strictPort: true,
        hmr: {
            host: 'localhost',
        },
    },
    plugins: [
        laravel({
            input: [
                'public/template/default/css/app.css',
                'public/template/default/css/swiper-bundle.min.css',
                'public/template/default/css/responsive.css',
                'public/template/default/js/swiper-bundle.min.js',
                'public/template/default/js/app.js',
            ],
            publicDirectory: 'public', // если assets лежат в public
            buildDirectory: 'build', // папка, куда Vite будет складывать ассеты
            refresh: true,
        }),
    ],
});
