# Laravel 框架文件
/.phpunit.cache
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/vendor
.env
.env.backup
.env.production
.env.local
.env.testing
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log

# IDE 和编辑器
/.fleet
/.idea
/.vscode
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# 日志文件
*.log
/storage/logs/*.log

# 缓存文件
/bootstrap/cache/*.php
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*

# 上传文件
/storage/app/public/*
!/storage/app/public/.gitkeep

# 编译资源
/public/css/app.css
/public/js/app.js
/public/mix-manifest.json

# 包管理器
package-lock.json
yarn.lock
composer.lock

# 开发工具
.phpunit.result.cache
.php_cs.cache
.php-cs-fixer.cache

# Docker
.docker/
docker-compose.override.yml

# 临时文件
*.tmp
*.temp
*.bak
*.backup

# 项目特定文件
/public/xxtt2.tar.gz
/public/test.php
composer-setup.exe

# 数据库
*.sqlite
*.db

# 配置文件（如果包含敏感信息）
# config/database.php
# config/mail.php
